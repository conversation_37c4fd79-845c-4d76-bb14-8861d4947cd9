{% liquid 
  assign section_type = 'product-essentials'
%}


{% render 'product-data' product:product, script_tag:true include_media:true  include_variants:true include_inventory:true,  preselect_variant:section.settings.preselect_variant, preselect_option:1, history_state:section.settings.history_state, include:'media' metafields:section.settings.metafields settings:section.settings %}


{{ 'products.js' | asset_url | script_tag }}

<section 
  class="section section--{{ section_type }} product-essentials {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
  style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}"
  >

  <main class="section__container relative {% render 'class-settings' prefix:'container_class', settings:section.settings %}">

    {% render 'flex-nested-blocks' blocks:section.blocks offset:0 %}

  </main> 

</section>

<script>
{% if section.settings.preselect_variant_js != blank %}
	window.addEventListener('Products:initialized', ()=>{
		if({{ section.settings.preselect_variant_js }} && Util.urlparams.parse().variant){
			Products.select.variant(null,Number(Util.urlparams.parse().variant))
		}
	})
{% endif %}


// const params = new URL(window.location.href).searchParams;
{% if settings.enable_logs -%}console.log("section.settings.preselect_variant_js", {{section.settings.preselect_variant_js}}){%- endif %}

let preselect_variant_js_val = false;
{% if section.settings.preselect_variant_js != blank %}
	preselect_variant_js_val = {{section.settings.preselect_variant_js}};
{% endif %} 
{% if settings.enable_logs -%}console.log("preselect_variant_js_val ", preselect_variant_js_val){%- endif %}

if (!preselect_variant_js_val && {{ section.settings.enable_preselect_variant_index }}) {
	
	// Function to get available sizes with full objects
	const getavailableSizes = (data) => {
	// Find the Size option
	const sizeOption = data.options.find(option => option.name === 'Size');
	
	if (!sizeOption) {
		return []; // Return empty array if Size option is not found
	}

	// Filter available sizes and store the entire object
	const availabilityModel = 'progressive';
	{% if settings.enable_logs -%}console.log("availabilityModel ",availabilityModel){%- endif %}
	const availableSizes = sizeOption.values.filter(size => {
		return size.availability[availabilityModel].available; // Check if the size is available
	});

	return availableSizes;
	};

	let pre_select_index = {{ section.settings.preselect_variant_index | json }};
	{% if settings.enable_logs -%}console.log("pre_select_index ", pre_select_index);{%- endif %}

	// Ensure pre_select_index is at least 1
	pre_select_index = Math.max(pre_select_index, 1);

	const handlePreselect = () => {
		const product = window.products['{{ product.handle }}'];
		// Get available sizes with full details and output the result
		const availableSizes = getavailableSizes(product);
		{% if settings.enable_logs -%}console.log("availableSizes ",availableSizes);{%- endif %}

		const variants = product.variants;
		{% if settings.enable_logs -%}console.log("variants", variants);{%- endif %}

		// Ensure index is within bounds
		const index = Math.min(availableSizes.length - 1, Math.max(pre_select_index - 1, 0));
		const preferredVariantId = availableSizes[index] || null;

		{% if settings.enable_logs -%}console.log("index ", index, "preferredVariantId ", preferredVariantId){%- endif %}

		// If a preferred variant ID was found, pass it to the Products.select.variant function
		if (preferredVariantId !== null) {
		//Products.select.variant(null, preferredVariantId);
		Products.select.option('{{ product.handle }}', 'Size', preferredVariantId.value);
		}
	};

	// Wait for both Products and Alpine to be ready
	let productsReady = false;
	let alpineReady = false;

	const tryPreselect = () => {
		if (productsReady && alpineReady) {
			handlePreselect();
		}
	};

	window.addEventListener('Products:initialized', () => {
		productsReady = true;
		tryPreselect();
	});

	document.addEventListener('alpine:initialized', () => {
		alpineReady = true;
		tryPreselect();
	});
}
</script>
  

{% schema %}
{
	"name": "Product Essentials",
	"tag": "section",
	"class": "product-essentials",
	"settings": [
		{
			"type": "header",
			"content": "Product Data"
		},
		{
			"type": "liquid",
			"id": "metafields",
			"label": "Metafields to be included in dynamic data",
			"info": "Comma-separated string of metafields, in the format namespace.key"
		},
		{
			"type": "checkbox",
			"id": "history_state",
			"label": "Product State Change",
			"default": true,
			"info": "Change URL and product gallery on product sibling selection"
		},
		{
			"type": "checkbox",
			"id": "preselect_variant",
			"label": "Pre-select Variant",
			"default": false
		},
		{
			"type": "liquid",
			"id": "preselect_variant_js",
			"label": "Pre-select Variant",
			"info": "[JavaScript] An expression which evaluates to a truthy value will preselect the variant that is specified by the `variant` query parameter of the url"
		},
		{
			"type": "checkbox",
			"id": "enable_preselect_variant_index",
			"label": "Enable Pre-select variant index",
			"default": false
		},
		{
			"type": "range",
			"label": "Pre-select variant index",
			"id": "preselect_variant_index",
			"default": 1,
			"info": "Enter the index of the variant to pre-select.",
			"min": 1,
			"max": 10
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "header",
			"content": "@global: Container Settings"
		},
		{
			"type": "select",
			"id": "container_class_container",
			"label": "@global:  Container",
			"options": [
				{
					"value": "w-full",
					"label": "Full Screen Width"
				},
				{
					"value": "container",
					"label": "Container Width"
				},
				{
					"value": "container container--wide",
					"label": "Wide Container"
				},
				{
					"value": "container container--narrow",
					"label": "Narrow Container"
				},
				{
					"value": "container container--tight",
					"label": "Very Narrow Container"
				},
				{
					"value": "container--product",
					"label": "Product Container Width"
				},
				{
					"value": "container--wide",
					"label": "Wide Container Width"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding",
			"label": "@global:  Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding",
			"label": "@global:  Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding_desktop",
			"label": "@global:  Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding_desktop",
			"label": "@global:  Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		}
	],
	"blocks": [
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Frame",
					"info": "Renames the Frame block, and also gets added as an ID to the DOM element in a handleized form."
				},
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "article_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "liquid",
					"label": "@global: Liquid Logic Inclusion",
					"id": "inclusion_liquid",
					"info": "Insert any liquid logic that returns a value to display the section",
					"default": "true"
				},
				{
					"type": "textarea",
					"label": "@global: Javascript Logic Inclusion",
					"id": "inclusion_js",
					"info": "Insert any javascript logic that evaluates to true to display the section"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "header",
					"content": "@global: Frame Width Settings"
				},
				{
					"type": "select",
					"id": "article_class_width",
					"label": "@global: Frame Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_width_desktop",
					"label": "@global: Frame Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "header",
					"content": "Frame Height Settings"
				},
				{
					"type": "select",
					"id": "article_class_height",
					"label": "Frame Height",
					"options": [
						{
							"value": "h-auto",
							"label": "Auto"
						},
						{
							"value": "h-full",
							"label": "100%"
						},
						{
							"value": "h-3/4",
							"label": "75%"
						},
						{
							"value": "h-1/2",
							"label": "50%"
						},
						{
							"value": "h-1/4",
							"label": "25%"
						},
						{
							"value": "h-screen",
							"label": "Full Screen"
						},
						{
							"value": "h-dvh",
							"label": "Dynamic Viewport"
						},
						{
							"value": "h-lvh",
							"label": "Largest Viewport"
						},
						{
							"value": "h-svh",
							"label": "Smallest Viewport"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_height_desktop",
					"label": "Frame Height Desktop",
					"options": [
						{
							"value": "lg:h-auto",
							"label": "Auto"
						},
						{
							"value": "lg:h-full",
							"label": "100%"
						},
						{
							"value": "lg:h-3/4",
							"label": "75%"
						},
						{
							"value": "lg:h-1/2",
							"label": "50%"
						},
						{
							"value": "lg:h-1/4",
							"label": "25%"
						},
						{
							"value": "lg:h-screen",
							"label": "Full Screen"
						},
						{
							"value": "lg:h-main",
							"label": "Full Screen minus Header"
						}
					]
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "color_background",
					"id": "article_style_background",
					"label": "Frame Background Gradient"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "header",
					"content": "@global: Frame Layout"
				},
				{
					"type": "select",
					"id": "article_class_display",
					"label": "@global: Frame Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_display_desktop",
					"label": "@global: Frame Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_direction",
					"label": "@global: Frame Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_layout",
					"label": "@global: Frame Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_layout_spacing",
					"label": "@global: Frame Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_vertical_padding",
					"label": "@global: Frame Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_horizontal_padding",
					"label": "@global: Frame Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_gap",
					"label": "@global: Frame Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_vertical_padding_desktop",
					"label": "@global: Frame Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_horizontal_padding_desktop",
					"label": "@global: Frame Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_gap_desktop",
					"label": "@global: Frame Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "header",
					"content": "Position Settings"
				},
				{
					"type": "select",
					"id": "article_class_position",
					"label": "Position",
					"default": "relative",
					"options": [
						{
							"value": "",
							"label": "Unset"
						},
						{
							"value": "relative",
							"label": "Relative"
						},
						{
							"value": "absolute",
							"label": "Absolute"
						},
						{
							"value": "sticky top-0",
							"label": "Sticky Top"
						},
						{
							"value": "sticky bottom-0",
							"label": "Sticky Bottom"
						},
						{
							"value": "fixed top-0",
							"label": "Fixed Top"
						},
						{
							"value": "fixed bottom-0",
							"label": "Fixed Bottom"
						}
					]
				},
				{
					"type": "range",
					"id": "article_style___offset_y",
					"label": "Y Offset",
					"min": -100,
					"max": 100,
					"step": 2,
					"unit": "%",
					"default": 0,
					"visible_if": "{{ block.settings.article_class_position contains 'sticky' or block.settings.article_class_position contains 'fixed' }}"
				},
				{
					"type": "select",
					"id": "article_class_z-index",
					"label": "Z-Index",
					"default": "",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "z-1",
							"label": "Z-1"
						},
						{
							"value": "z-10",
							"label": "Z-10"
						},
						{
							"value": "z-20",
							"label": "Z-20"
						},
						{
							"value": "z-30",
							"label": "Z-20"
						},
						{
							"value": "z-40",
							"label": "Z-40"
						},
						{
							"value": "z-50",
							"label": "Z-50"
						}
					],
					"visible_if": "{{ block.settings.article_class_position != '' }}"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "select",
					"id": "article_class_overflow",
					"label": "Horizontal Overflow",
					"default": "",
					"options": [
						{
							"value": "",
							"label": "Contain"
						},
						{
							"value": "overflow-x-scroll snap-none overscroll-x-contain",
							"label": "Smooth Scroll"
						},
						{
							"value": "overflow-x-scroll snap-x snap-start",
							"label": "Snap Start"
						},
						{
							"value": "overflow-x-scroll snap-x snap-center",
							"label": "Snap Center"
						}
					]
				},
				{
					"type": "liquid",
					"id": "article_class_custom_classes",
					"label": "Custom Classes"
				},
				{
					"type": "liquid",
					"id": "article_attr_x_data",
					"label": "Dynamic Data Source"
				},
				{
					"type": "liquid",
					"id": "article_attr_x_if",
					"label": "JS Conditional Rendering"
				},
				{
					"type": "text",
					"id": "teleport",
					"label": "Teleport",
					"info": "Display frame in a different location in the DOM. Enter the ID of the target element."
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Frame Break"
				}
			]
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "header",
					"content": "@global: Item Width Settings"
				},
				{
					"type": "select",
					"id": "item_class_width",
					"label": "@global: Item Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_width_desktop",
					"label": "@global: Item Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Aspect, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "select",
					"id": "item_class_aspect",
					"label": "@global: Item Aspect Ratio",
					"options": [
						{
							"value": "aspect-auto",
							"label": "Auto"
						},
						{
							"value": "aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_aspect_desktop",
					"label": "@global: Item Aspect Ratio Desktop",
					"options": [
						{
							"value": "lg:aspect-auto",
							"label": "Auto"
						},
						{
							"value": "lg:aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "lg:aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "lg:aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "lg:aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "lg:aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "lg:aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "lg:aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "lg:aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:item_class, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "item_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:item_class, label_prefix:Item, default_direction:flex-col, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Item Layout"
				},
				{
					"type": "select",
					"id": "item_class_display",
					"label": "@global: Item Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_display_desktop",
					"label": "@global: Item Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_direction",
					"label": "@global: Item Direction",
					"default": "flex-col",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_layout",
					"label": "@global: Item Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_layout_spacing",
					"label": "@global: Item Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_vertical_padding",
					"label": "@global: Item Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_horizontal_padding",
					"label": "@global: Item Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_gap",
					"label": "@global: Item Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_vertical_padding_desktop",
					"label": "@global: Item Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_horizontal_padding_desktop",
					"label": "@global: Item Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_gap_desktop",
					"label": "@global: Item Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "color_background",
					"id": "item_style_background",
					"label": "@global: Item Background Color"
				},
				{
					"type": "color_background",
					"id": "content_style_background",
					"label": "@global: Content Background Color"
				},
				{
					"type": "header",
					"content": "@global: Content Interactivity"
				},
				{
					"type": "url",
					"id": "link",
					"label": "@global: Link"
				},
				{
					"type": "liquid",
					"id": "liquid_link",
					"label": "@global: Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅜🅔🅓🅘🅐, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅜🅔🅓🅘🅐\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "@global: Media"
				},
				{
					"type": "image_picker",
					"label": "@global: Image",
					"id": "image"
				},
				{
					"type": "image_picker",
					"label": "@global: Image (Desktop)",
					"id": "image_desktop"
				},
				{
					"type": "select",
					"label": "@global: Image Position",
					"id": "image_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full object-cover"
						}
					]
				},
				{
					"type": "select",
					"label": "@global: Loading",
					"id": "image_loading",
					"options": [
						{
							"label": "Lazy",
							"value": "lazy"
						},
						{
							"label": "Eager",
							"value": "eager"
						}
					],
					"default": "eager"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:media_class, label_prefix:Media , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Media Width Settings"
				},
				{
					"type": "select",
					"id": "media_class_width",
					"label": "@global: Media Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "media_class_width_desktop",
					"label": "@global: Media Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "text",
					"label": "@global: Video",
					"id": "video"
				},
				{
					"type": "text",
					"label": "@global: Video For Mobile",
					"id": "videomobile"
				},
				{
					"type": "select",
					"label": "@global: Video Position",
					"id": "video_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full"
						}
					]
				},
				{
					"type": "select",
					"label": "@global: Video Position For Mobile",
					"id": "videomobile_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full"
						}
					]
				},
				{
					"type": "image_picker",
					"label": "@global: Video Poster For Desktop",
					"id": "posterDesktop"
				},
				{
					"type": "image_picker",
					"label": "@global: Video Poster for Mobile",
					"id": "posterMobile"
				},
				{
					"type": "checkbox",
					"label": "@global: Autoplay",
					"id": "video_attr_autoplay",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "@global: Muted",
					"id": "video_attr_muted",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "@global: Loop",
					"id": "video_attr_loop",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "@global: Controls",
					"id": "video_attr_controls",
					"default": false
				},
				{
					"type": "checkbox",
					"label": "@global: Playsinline",
					"id": "video_attr_playsinline",
					"default": true
				},
				{
					"type": "header",
					"content": "@global: Video Defer Settings"
				},
				{
					"type": "select",
					"id": "loading_method",
					"label": "@global: Video Loading Method",
					"options": [
						{
							"value": "immediate",
							"label": "Immediate"
						},
						{
							"value": "scroll",
							"label": "On Scroll"
						},
						{
							"value": "hover",
							"label": "On Hover"
						},
						{
							"value": "time",
							"label": "After Delay"
						}
					],
					"default": "scroll"
				},
				{
					"type": "range",
					"id": "loading_delay",
					"label": "@global: Loading Delay (seconds)",
					"min": 0,
					"max": 10,
					"step": 1,
					"default": 2,
					"info": "Only applies when loading method is After Delay"
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅒🅞🅝🅣🅔🅝🅣 🅛🅐🅨🅞🅤🅣, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅒🅞🅝🅣🅔🅝🅣 🅛🅐🅨🅞🅤🅣\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:content_class, label_prefix:Content , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Content Width Settings"
				},
				{
					"type": "select",
					"id": "content_class_width",
					"label": "@global: Content Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_width_desktop",
					"label": "@global: Content Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:content_class, label_prefix:Content, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Content Layout"
				},
				{
					"type": "select",
					"id": "content_class_display",
					"label": "@global: Content Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_display_desktop",
					"label": "@global: Content Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_direction",
					"label": "@global: Content Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_layout",
					"label": "@global: Content Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_layout_spacing",
					"label": "@global: Content Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_vertical_padding",
					"label": "@global: Content Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_horizontal_padding",
					"label": "@global: Content Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_gap",
					"label": "@global: Content Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_vertical_padding_desktop",
					"label": "@global: Content Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_horizontal_padding_desktop",
					"label": "@global: Content Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_gap_desktop",
					"label": "@global: Content Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Aspect, id_prefix:content_class, label_prefix:Content , @extends:ContentItem"
				},
				{
					"type": "select",
					"id": "content_class_aspect",
					"label": "@global: Content Aspect Ratio",
					"options": [
						{
							"value": "aspect-auto",
							"label": "Auto"
						},
						{
							"value": "aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_aspect_desktop",
					"label": "@global: Content Aspect Ratio Desktop",
					"options": [
						{
							"value": "lg:aspect-auto",
							"label": "Auto"
						},
						{
							"value": "lg:aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "lg:aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "lg:aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "lg:aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "lg:aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "lg:aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "lg:aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "lg:aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅣🅔🅧🅣 🅢🅣🅐🅒🅚, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅣🅔🅧🅣 🅢🅣🅐🅒🅚\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "@global: Text Stack"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:text_stack_class, label_prefix:Text Stack , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text Stack Width Settings"
				},
				{
					"type": "select",
					"id": "text_stack_class_width",
					"label": "@global: Text Stack Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_width_desktop",
					"label": "@global: Text Stack Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:text_stack_class, label_prefix:Text, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text Layout"
				},
				{
					"type": "select",
					"id": "text_stack_class_display",
					"label": "@global: Text Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_display_desktop",
					"label": "@global: Text Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_direction",
					"label": "@global: Text Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_layout",
					"label": "@global: Text Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_layout_spacing",
					"label": "@global: Text Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_vertical_padding",
					"label": "@global: Text Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_horizontal_padding",
					"label": "@global: Text Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_gap",
					"label": "@global: Text Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_vertical_padding_desktop",
					"label": "@global: Text Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_horizontal_padding_desktop",
					"label": "@global: Text Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_gap_desktop",
					"label": "@global: Text Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "image_picker",
					"label": "@global: Title Image",
					"id": "title_image"
				},
				{
					"type": "liquid",
					"id": "svg",
					"label": "@global: SVG"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:title_image_class, label_prefix:Title Image / SVG , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Title Image / SVG Width Settings"
				},
				{
					"type": "select",
					"id": "title_image_class_width",
					"label": "@global: Title Image / SVG Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "title_image_class_width_desktop",
					"label": "@global: Title Image / SVG Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "radio",
					"label": "@global: Text Justification",
					"id": "text_stack_class",
					"default": "text-center",
					"options": [
						{
							"label": "←",
							"value": "text-left"
						},
						{
							"label": "↔",
							"value": "text-center"
						},
						{
							"label": "→",
							"value": "text-right"
						}
					]
				},
				{
					"type": "color",
					"id": "content_style_color",
					"label": "@global: Text Color"
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text 1 Settings"
				},
				{
					"type": "text",
					"id": "text_item_1_text",
					"label": "@global: Text 1 Text"
				},
				{
					"type": "liquid",
					"id": "text_item_1_liquid",
					"label": "@global: Text 1 Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_item_1_attr_x_text",
					"label": "@global: Text 1 Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_item_1_element",
					"label": "@global: Text 1 Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_1_class_type_style",
					"label": "@global: Text 1 Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_1_class_type_size",
					"label": "@global: Text 1 Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_1_style_color",
					"label": "@global: Text 1 Color"
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_item_2, label_prefix:Text 2 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text 2 Settings"
				},
				{
					"type": "text",
					"id": "text_item_2_text",
					"label": "@global: Text 2 Text"
				},
				{
					"type": "liquid",
					"id": "text_item_2_liquid",
					"label": "@global: Text 2 Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_item_2_attr_x_text",
					"label": "@global: Text 2 Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_item_2_element",
					"label": "@global: Text 2 Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_2_class_type_style",
					"label": "@global: Text 2 Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_2_class_type_size",
					"label": "@global: Text 2 Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_2_style_color",
					"label": "@global: Text 2 Color"
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_item_3, label_prefix:Text 3 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text 3 Settings"
				},
				{
					"type": "text",
					"id": "text_item_3_text",
					"label": "@global: Text 3 Text"
				},
				{
					"type": "liquid",
					"id": "text_item_3_liquid",
					"label": "@global: Text 3 Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_item_3_attr_x_text",
					"label": "@global: Text 3 Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_item_3_element",
					"label": "@global: Text 3 Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_3_class_type_style",
					"label": "@global: Text 3 Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_3_class_type_size",
					"label": "@global: Text 3 Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_3_style_color",
					"label": "@global: Text 3 Color"
				},
				{
					"type": "header",
					"content": "@global: Custom Liquid"
				},
				{
					"type": "liquid",
					"id": "liquid",
					"label": "@global: Custom Liquid/HTML"
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅑🅤🅣🅣🅞🅝 🅢🅔🅣, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅑🅤🅣🅣🅞🅝 🅢🅔🅣\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "@global: Button Settings"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout id_prefix:buttons_, label_prefix:Buttons, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Buttons Layout"
				},
				{
					"type": "select",
					"id": "buttons__display",
					"label": "@global: Buttons Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__display_desktop",
					"label": "@global: Buttons Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__direction",
					"label": "@global: Buttons Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__layout",
					"label": "@global: Buttons Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__layout_spacing",
					"label": "@global: Buttons Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__vertical_padding",
					"label": "@global: Buttons Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__horizontal_padding",
					"label": "@global: Buttons Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__gap",
					"label": "@global: Buttons Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__vertical_padding_desktop",
					"label": "@global: Buttons Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__horizontal_padding_desktop",
					"label": "@global: Buttons Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__gap_desktop",
					"label": "@global: Buttons Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Button id_prefix:button_1, label_prefix:Button 1 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Button 1 Settings"
				},
				{
					"type": "text",
					"id": "button_1_text",
					"label": "@global: Button 1 Text"
				},
				{
					"type": "text",
					"id": "button_1_leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "button_1_trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "url",
					"id": "button_1_link",
					"label": "@global: Button 1 Link"
				},
				{
					"type": "liquid",
					"id": "button_1_liquid_link",
					"label": "@global: Button 1 Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "liquid",
					"id": "button_1_onclick",
					"label": "@global: Button 1 On Click"
				},
				{
					"type": "select",
					"id": "button_1_class_style",
					"label": "@global: Button 1 Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_1_class_size",
					"label": "@global: Button 1 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Button id_prefix:button_2, label_prefix:Button 2 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Button 2 Settings"
				},
				{
					"type": "text",
					"id": "button_2_text",
					"label": "@global: Button 2 Text"
				},
				{
					"type": "text",
					"id": "button_2_leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "button_2_trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "url",
					"id": "button_2_link",
					"label": "@global: Button 2 Link"
				},
				{
					"type": "liquid",
					"id": "button_2_liquid_link",
					"label": "@global: Button 2 Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "liquid",
					"id": "button_2_onclick",
					"label": "@global: Button 2 On Click"
				},
				{
					"type": "select",
					"id": "button_2_class_style",
					"label": "@global: Button 2 Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_2_class_size",
					"label": "@global: Button 2 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Button id_prefix:button_3, label_prefix:Button 3 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Button 3 Settings"
				},
				{
					"type": "text",
					"id": "button_3_text",
					"label": "@global: Button 3 Text"
				},
				{
					"type": "text",
					"id": "button_3_leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "button_3_trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "url",
					"id": "button_3_link",
					"label": "@global: Button 3 Link"
				},
				{
					"type": "liquid",
					"id": "button_3_liquid_link",
					"label": "@global: Button 3 Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "liquid",
					"id": "button_3_onclick",
					"label": "@global: Button 3 On Click"
				},
				{
					"type": "select",
					"id": "button_3_class_style",
					"label": "@global: Button 3 Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_3_class_size",
					"label": "@global: Button 3 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				}
			]
		},
		{
			"type": "product-media-grid",
			"name": "Product Media Grid",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:media_grid_class"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "media_grid_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "select",
					"id": "media_grid_class_grid",
					"label": "Grid Items",
					"options": [
						{
							"label": "1-Across",
							"value": "grid-cols-1"
						},
						{
							"label": "2-Across",
							"value": "grid-cols-2"
						}
					]
				},
				{
					"type": "select",
					"id": "media_grid_class_gap",
					"label": "Frame Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "media_grid_class_grid_desktop",
					"label": "Grid Items (Desktop)",
					"options": [
						{
							"label": "1-Across",
							"value": "grid-cols-1"
						},
						{
							"label": "2-Across",
							"value": "grid-cols-2"
						},
						{
							"label": "3-Across",
							"value": "grid-cols-3"
						}
					]
				},
				{
					"type": "select",
					"id": "media_grid_class_gap_desktop",
					"label": "Buttons Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "media_grid_modal_zoom",
					"label": "Zoom/Modal",
					"options": [
						{
							"label": "Zoom",
							"value": "zoom_hover_over_click"
						},
						{
							"label": "Modal",
							"value": "modal_over_click"
						}
					]
				},
				{
					"type": "number",
					"label": "Full Width Image Number",
					"id": "full_width_index",
					"default": 3,
					"info": "If gallery has a media count of an odd number, this will assign a full-width image at a specific index. For best design practice, set the value to an odd number."
				},
				{
					"type": "checkbox",
					"id": "even_number_image_width",
					"label": "Adjust Media Width for Even Image Count"
				},
				{
					"type": "text",
					"label": "Full Width Image Alt Text Key Words",
					"id": "full_width_words",
					"info": "If gallery has a media count of an odd number, this will assign any image with alt text containing these words will be set to full width."
				},
				{
					"type": "text",
					"label": "Video Media Attributes",
					"id": "media_item_attr",
					"info": "Allows direct access to native web video attributes, i.e. 'muted', 'autoplay'.",
					"default": "muted autoplay loop playsinline controls disablepictureinpicture controlslist=\"nodownload noplaybackrate\""
				},
				{
					"type": "checkbox",
					"id": "additional_video_attributes",
					"label": "Enable Additional Attributes through alt text"
				},
				{
					"type": "color",
					"id": "media_item_style_background_color",
					"label": "Media Item Background Color"
				},
				{
					"type": "color_background",
					"id": "media_item_style_background_css",
					"label": "Media Item Background Style"
				},
				{
					"type": "checkbox",
					"id": "grid_hide_model_image",
					"label": "Hide Media",
					"info": "Hide media using the below keyword in the alt text."
				},
				{
					"type": "text",
					"id": "grid_hide_alt_txt",
					"label": "Hidden Media alt texts",
					"info": "Any images containing the above alt texts will be hidden in the Product Media"
				},
				{
					"type": "header",
					"content": "Video Defer Settings"
				},
				{
					"type": "select",
					"id": "loading_method",
					"label": "Video Loading Method",
					"options": [
						{
							"value": "immediate",
							"label": "Immediate"
						},
						{
							"value": "scroll",
							"label": "On Scroll"
						},
						{
							"value": "hover",
							"label": "On Hover"
						},
						{
							"value": "time",
							"label": "After Delay"
						}
					],
					"default": "scroll"
				},
				{
					"type": "range",
					"id": "loading_delay",
					"label": "Loading Delay (seconds)",
					"min": 0,
					"max": 10,
					"step": 1,
					"default": 2,
					"info": "Only applies when loading method is After Delay"
				}
			]
		},
		{
			"type": "product-media-carousel",
			"name": "Product Media Carousel",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:media_carousel_class"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "media_carousel_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "select",
					"id": "media_carousel_pagination",
					"label": "Crousel Pagination Type",
					"default": "bullets",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "bullets",
							"label": "Bullets"
						},
						{
							"value": "progressbar",
							"label": "Progress Bar"
						}
					]
				},
				{
					"type": "number",
					"id": "media_items_per_view",
					"label": "Media Items Per View"
				},
				{
					"type": "text",
					"label": "Video Media Attributes",
					"id": "media_item_attr",
					"info": "Allows direct access to native web video attributes, i.e. 'muted', 'autoplay'.",
					"default": "muted autoplay loop playsinline"
				},
				{
					"type": "checkbox",
					"id": "additional_video_attributes",
					"label": "Enable Additional Attributes through alt text"
				},
				{
					"type": "color",
					"id": "media_item_style_background_color",
					"label": "Media Item Background Color"
				},
				{
					"type": "color_background",
					"id": "media_item_style_background_css",
					"label": "Media Item Background Style"
				},
				{
					"type": "checkbox",
					"id": "show_play_button",
					"label": "Show Play Button",
					"default": true,
					"info": "Show a play button when a video is present in the Products' media. Disabling this will hide the play button even if a video exists."
				},
				{
					"type": "select",
					"id": "media_grid_modal_zoom",
					"label": "Zoom/Modal",
					"options": [
						{
							"label": "Zoom",
							"value": "zoom_hover_over_click"
						},
						{
							"label": "Modal",
							"value": "modal_over_click"
						}
					]
				},
				{
					"type": "checkbox",
					"id": "carousel_hide_model_image",
					"label": "Hide Media",
					"info": "Hide media using the below keyword in the alt text."
				},
				{
					"type": "text",
					"id": "carousel_hide_alt_txt",
					"label": "Hidden Media alt texts",
					"info": "Any images containing the above alt texts will be hidden in the Product Media"
				},
				{
					"type": "header",
					"content": "Video Defer Settings"
				},
				{
					"type": "select",
					"id": "loading_method",
					"label": "Video Loading Method",
					"options": [
						{
							"value": "immediate",
							"label": "Immediate"
						},
						{
							"value": "scroll",
							"label": "On Scroll"
						},
						{
							"value": "hover",
							"label": "On Hover"
						},
						{
							"value": "time",
							"label": "After Delay"
						}
					],
					"default": "scroll"
				},
				{
					"type": "range",
					"id": "loading_delay",
					"label": "Loading Delay (seconds)",
					"min": 0,
					"max": 10,
					"step": 1,
					"default": 2,
					"info": "Only applies when loading method is After Delay"
				}
			]
		},
		{
			"type": "product-header",
			"name": "Product Header",
			"settings": [
				{
					"type": "product",
					"id": "product",
					"label": "Product"
				},
				{
					"type": "paragraph",
					"content": "@include ProductHeader"
				},
				{
					"type": "liquid",
					"id": "title_source",
					"label": "@global: Product Title Source",
					"info": "Liquid to extract the product title"
				},
				{
					"type": "liquid",
					"id": "metafield_subtitle_value",
					"label": "@global: Product Metafield Subtitle"
				},
				{
					"type": "checkbox",
					"id": "meta_subtitle_check",
					"label": "@global: Display Subtitle",
					"default": false
				},
				{
					"type": "checkbox",
					"id": "price",
					"label": "@global: Display Price",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "type",
					"label": "@global: Display Product Type",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "reviews",
					"label": "@global: Display Review Summary",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "display_dynamic_product_title",
					"label": "@global: Display Dynamic Product Title",
					"default": false
				}
			]
		},
		{
			"type": "short-description",
			"name": "Short Description",
			"settings": [
				{
					"type": "richtext",
					"id": "description",
					"label": "Short Description"
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:title, label_prefix:Title"
				},
				{
					"type": "header",
					"content": "@global: Title Settings"
				},
				{
					"type": "text",
					"id": "title_text",
					"label": "@global: Title Text"
				},
				{
					"type": "liquid",
					"id": "title_liquid",
					"label": "@global: Title Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "title_attr_x_text",
					"label": "@global: Title Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "title_element",
					"label": "@global: Title Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "title_class_type_style",
					"label": "@global: Title Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "title_class_type_size",
					"label": "@global: Title Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "title_style_color",
					"label": "@global: Title Color"
				}
			]
		},
		{
			"type": "text",
			"name": "Text",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:text, label_prefix:Text, type:richtext"
				},
				{
					"type": "header",
					"content": "@global: Text Settings"
				},
				{
					"type": "richtext",
					"id": "text_text",
					"label": "@global: Text Text"
				},
				{
					"type": "liquid",
					"id": "text_liquid",
					"label": "@global: Text Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_attr_x_text",
					"label": "@global: Text Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_element",
					"label": "@global: Text Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_type_style",
					"label": "@global: Text Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_type_size",
					"label": "@global: Text Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_style_color",
					"label": "@global: Text Color"
				}
			]
		},
		{
			"type": "breadcrumbs",
			"name": "Breadcrumbs",
			"settings": []
		},
		{
			"type": "custom_liquid",
			"name": "Liquid",
			"limit": 4,
			"settings": [
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Liquid Block"
				}
			]
		},
		{
			"type": "product_form",
			"name": "⤷ Product Form",
			"settings": [
				{
					"type": "select",
					"id": "availability_model",
					"label": "Option Availability Model",
					"default": "progressive",
					"options": [
						{
							"value": "cascade",
							"label": "Cascade"
						},
						{
							"value": "progressive",
							"label": "Progressive"
						},
						{
							"value": "reflexive",
							"label": "Reflexive"
						}
					]
				},
				{
					"content": "@include ProductForm",
					"type": "paragraph"
				},
				{
					"type": "product",
					"id": "product",
					"label": "@global: Product"
				}
			]
		},
		{
			"type": "variant-selector",
			"name": "Variant Selector",
			"settings": [
				{
					"type": "product",
					"id": "product",
					"label": "Product"
				},
				{
					"content": "@include VariantSelector",
					"type": "paragraph"
				},
				{
					"type": "checkbox",
					"id": "history_state",
					"label": "@global: Product State Change",
					"default": true,
					"info": "Change URL and product gallery on product sibling selection"
				},
				{
					"type": "select",
					"id": "availability_model",
					"label": "@global: Option Availability Model",
					"default": "progressive",
					"options": [
						{
							"value": "cascade",
							"label": "Cascade"
						},
						{
							"value": "progressive",
							"label": "Progressive"
						},
						{
							"value": "reflexive",
							"label": "Reflexive"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@global: Cascade: Evaluates availability top-down. Selections in earlier options impact subsequent ones but not vice versa. For instance, choosing in option 1 affects options 2 and 3, but a choice in option 2 only impacts option 3.\n2."
				},
				{
					"type": "paragraph",
					"content": "@global: Progressive: Assesses product options based on all previous choices, successively refining available selections.\n3."
				},
				{
					"type": "paragraph",
					"content": "@global: Reflexive: Availability is dictated only by the latest selection, disregarding prior choices."
				},
				{
					"type": "text",
					"id": "unselected",
					"label": "@global: Unselected Button Text",
					"default": "Select a Size"
				},
				{
					"type": "text",
					"id": "unavailable",
					"label": "@global: Unavailable Button Text",
					"info": "For option combinations that do not have a matching variant",
					"default": "Unavailable"
				},
				{
					"type": "text",
					"id": "out_of_stock",
					"label": "@global: Out of Stock Button Text",
					"default": "Out of Stock"
				},
				{
					"type": "checkbox",
					"id": "enable_strikethrough",
					"label": "@global: Enable Strikethrough on Unavailable products",
					"default": true
				},
				{
					"type": "text",
					"id": "available",
					"label": "@global: Available (Add to Cart) Button Text",
					"default": "Unavailable"
				},
				{
					"type": "range",
					"id": "variants_displayed_on_mobile",
					"label": "@global: Displayed Variants On Mobile",
					"min": 3,
					"max": 6,
					"step": 0.1,
					"default": 3.5
				},
				{
					"type": "checkbox",
					"id": "color_option_wrap_mobile",
					"label": "@global: Wrap Color Option On Mobile"
				},
				{
					"type": "range",
					"id": "variants_displayed_on_desktop",
					"label": "@global: Displayed Variants On Desktop",
					"min": 3,
					"max": 12,
					"step": 1,
					"default": 4
				},
				{
					"type": "header",
					"content": "@global: Product Siblings Settings"
				},
				{
					"type": "liquid",
					"id": "siblings_collection",
					"label": "@global: Product Siblings Collection"
				},
				{
					"type": "product_list",
					"id": "sibling_products",
					"label": "@global: Product Siblings",
					"info": "Specific products override Collection"
				},
				{
					"type": "header",
					"content": "@global: Size Guide Button"
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "@global: Disable Button",
					"default": true
				},
				{
					"type": "text",
					"id": "title",
					"label": "@global: Title",
					"info": "Internal Reference only"
				},
				{
					"type": "liquid",
					"id": "inclusion_js",
					"label": "@global: Inclusion Logic (JavaScript)",
					"info": "JavaScript code that evaluates to a non-false value will output this button"
				},
				{
					"type": "select",
					"id": "style",
					"label": "@global: Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_class_size",
					"label": "@global: Button Size",
					"default": "",
					"options": [
						{
							"value": "button--small",
							"label": "Small"
						},
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "@global: Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "liquid",
					"id": "onclick",
					"label": "@global: Click Event",
					"info": "JavaScript onclick event"
				},
				{
					"type": "header",
					"content": "@global: Variant Segments"
				},
				{
					"type": "select",
					"id": "variant_segment_design",
					"label": "@global: Design",
					"default": "tabs",
					"options": [
						{
							"value": "tabs",
							"label": "Tabs"
						},
						{
							"value": "list",
							"label": "List"
						}
					]
				},
				{
					"type": "text",
					"id": "variant_segment",
					"label": "@global: Variant Segment",
					"info": "This is the namespace and key for the metafield of the variant. (ex. theme.variant_split)",
					"visible_if": "{{ block.settings.variant_segment_design == 'list' }}"
				},
				{
					"type": "text",
					"id": "variant_segment_default",
					"label": "@global: 'Default' Segment Title",
					"default": "Core",
					"visible_if": "{{ block.settings.variant_segment_design == 'list' }}"
				},
				{
					"type": "text",
					"id": "variant_segment_all",
					"label": "@global: 'All Variants' Segment Title",
					"default": "All",
					"info": "Label for the tab that shows all colors",
					"visible_if": "{{ block.settings.variant_segment_design == 'tabs' }}"
				},
				{
					"type": "range",
					"id": "variant_segment_max_tabs",
					"min": 1,
					"max": 5,
					"step": 1,
					"default": 4,
					"label": "@global: Maximum number of tabs to display",
					"visible_if": "{{ block.settings.variant_segment_design == 'tabs' }}"
				},
				{
					"type": "select",
					"id": "variant_segment_default_tab",
					"label": "@global: Default tab",
					"options": [
						{
							"value": "all",
							"label": "All"
						},
						{
							"value": "first",
							"label": "First Available Tab"
						},
						{
							"value": "plp_selection",
							"label": "Based on PLP Selection"
						}
					],
					"default": "all",
					"visible_if": "{{ block.settings.variant_segment_design == 'tabs' }}"
				},
				{
					"type": "select",
					"id": "variant_segment_class_margin",
					"label": "@global: Variant Segment Spacing",
					"default": "mt-md",
					"options": [
						{
							"value": "@include Spacing prop:mt",
							"label": "Inclusion"
						},
						{
							"value": "mt-0",
							"label": "@global: None"
						},
						{
							"value": "mt-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "mt-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "mt-xs",
							"label": "@global: XS"
						},
						{
							"value": "mt-sm",
							"label": "@global: SM"
						},
						{
							"value": "mt-md",
							"label": "@global: MD"
						},
						{
							"value": "mt-lg",
							"label": "@global: LG"
						},
						{
							"value": "mt-xl",
							"label": "@global: XL"
						},
						{
							"value": "mt-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "mt-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "textarea",
					"id": "option_exclude",
					"label": "@global: Option Exclusion",
					"info": "Add the names of any Variant Options you would like to exclude on a new line"
				},
				{
					"type": "header",
					"content": "@global: Alternate Layout"
				},
				{
					"type": "select",
					"id": "variant_selector_class_layout",
					"label": "@global: Layout",
					"options": [
						{
							"label": "Default",
							"value": ""
						},
						{
							"label": "Table",
							"value": "variant-selector--table"
						}
					],
					"default": ""
				},
				{
					"type": "header",
					"content": "@global: Product Color"
				},
				{
					"type": "select",
					"id": "product_color_option_type",
					"label": "@global: Product Color Option Type",
					"default": "image",
					"options": [
						{
							"label": "Image",
							"value": "image"
						},
						{
							"label": "Swatch Color",
							"value": "swatch"
						},
						{
							"label": "Swatch Image",
							"value": "swatch_image"
						}
					]
				},
				{
					"type": "color",
					"id": "product_default_swatch_color_1",
					"label": "@global: Product Default Swatch Color 1",
					"default": "#000000"
				},
				{
					"type": "checkbox",
					"id": "product_color_hover_info",
					"label": "@global: Product Color Hover Info",
					"default": false
				}
			]
		},
		{
			"name": "Wishlist Toggle",
			"type": "wishlist-toggle",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:button_class"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "button_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "select",
					"id": "button_class_position",
					"label": "Button Position",
					"default": "",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Top Right",
							"value": "absolute top-lg right-lg z-10"
						}
					]
				}
			]
		},
		{
			"name": "⤶ Form End",
			"type": "form-end",
			"settings": []
		},
		{
			"type": "bundle",
			"name": "Product Bundle",
			"settings": [
				{
					"type": "text",
					"id": "bundle_name",
					"label": "Bundle Name",
					"default": "Bundle"
				},
				{
					"type": "text",
					"id": "button_select_options",
					"label": "Unselected Button Text",
					"default": "Please Select Options"
				},
				{
					"type": "text",
					"id": "button_unavailable",
					"label": "Unavailable Button Text",
					"default": "Unavailable"
				},
				{
					"type": "text",
					"id": "button_out_of_stock",
					"label": "Out of Stock Button Text",
					"default": "Out of Stock"
				},
				{
					"type": "text",
					"id": "button_add_to_cart",
					"label": "Add to Cart Button Text",
					"default": "Add all to Cart"
				}
			]
		},
		{
			"type": "dynamic-text",
			"name": "Dynamic Text",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"info": "Internal Reference only"
				},
				{
					"type": "liquid",
					"id": "inclusion",
					"label": "Inclusion Logic (Liquid)",
					"info": "Liquid code that outputs anything other than blank will output this button",
					"default": "1"
				},
				{
					"type": "liquid",
					"id": "inclusion_js",
					"label": "Inclusion Logic (JavaScript)",
					"info": "JavaScript code that evaluates to a non-false value will output this button"
				},
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:text, label_prefix:Text, type:liquid"
				},
				{
					"type": "header",
					"content": "@global: Text Settings"
				},
				{
					"type": "liquid",
					"id": "text_text",
					"label": "@global: Text Text"
				},
				{
					"type": "liquid",
					"id": "text_liquid",
					"label": "@global: Text Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_attr_x_text",
					"label": "@global: Text Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_element",
					"label": "@global: Text Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_type_style",
					"label": "@global: Text Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_type_size",
					"label": "@global: Text Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_style_color",
					"label": "@global: Text Color"
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"info": "Internal Reference only"
				},
				{
					"type": "liquid",
					"id": "inclusion_js",
					"label": "Inclusion Logic (JavaScript)",
					"info": "JavaScript code that evaluates to a non-false value will output this button"
				},
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_class_size",
					"label": "Button Size",
					"default": "",
					"options": [
						{
							"value": "button--small",
							"label": "Small"
						},
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "Disable Button"
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "liquid",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event"
				},
				{
					"type": "select",
					"id": "onclick_type",
					"label": "Type of Click Event",
					"options": [
						{
							"value": "x-data @",
							"label": "Alpine"
						},
						{
							"value": "on",
							"label": "Native"
						}
					],
					"default": "x-data @"
				}
			]
		},
		{
			"type": "enunciation",
			"name": "Enunciation",
			"settings": [
				{
					"type": "header",
					"content": "Content Management"
				},
				{
					"type": "paragraph",
					"content": "This content is managed in the enunciation metafield on the product."
				}
			]
		},
		{
			"type": "fit_guide",
			"name": "Fit Guide",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title"
				},
				{
					"content": "@include FitGuide",
					"type": "paragraph"
				},
				{
					"type": "paragraph",
					"content": "@global: This feature is powered by tags. On a product the tag format follows: \n\"{Category}:{value}\""
				},
				{
					"type": "paragraph",
					"content": "@global: The product tag can use the option value or a numerical value. For Example: \"size:small\" or \"width:50\" "
				},
				{
					"type": "textarea",
					"label": "@global: Categories",
					"id": "categories"
				},
				{
					"type": "text",
					"label": "@global: Categories (Dynamic)",
					"id": "categories_dynamic"
				},
				{
					"type": "checkbox",
					"label": "@global: Make Collapsable",
					"id": "collapse"
				},
				{
					"type": "select",
					"label": "Shop Category Name on Top",
					"id": "brand_fit_guide",
					"options": [
						{
							"value": "left",
							"label": "Left"
						},
						{
							"value": "top",
							"label": "Top"
						}
					],
					"default": "left"
				},
				{
					"type": "product",
					"label": "Override Product",
					"id": "product"
				}
			]
		},
		{
			"name": "Divider",
			"type": "divider",
			"settings": [
				{
					"type": "color",
					"id": "divider_style_border_color",
					"label": "Color"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:divider_class, label_prefix:Divider "
				},
				{
					"type": "header",
					"content": "@global: Divider Width Settings"
				},
				{
					"type": "select",
					"id": "divider_class_width",
					"label": "@global: Divider Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "divider_class_width_desktop",
					"label": "@global: Divider Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				}
			]
		},
		{
			"name": "Spacer",
			"type": "spacer",
			"settings": [
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing",
					"label": "Item Vertical Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "pb-0",
							"label": "@global: None"
						},
						{
							"value": "pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "pb-md",
							"label": "@global: MD"
						},
						{
							"value": "pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pb-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing",
					"label": "Item Horizontal Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "pr-0",
							"label": "@global: None"
						},
						{
							"value": "pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "pr-md",
							"label": "@global: MD"
						},
						{
							"value": "pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pr-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing_desktop",
					"label": "Item Vertical Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "lg:pb-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pb-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pb-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pb-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pb-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pb-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pb-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pb-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing_desktop",
					"label": "Item Horizontal Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "lg:pr-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pr-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pr-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pr-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pr-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pr-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pr-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pr-8xl",
							"label": "@global: 8XL"
						}
					]
				}
			]
		},
		{
			"type": "payment_widget",
			"name": "Payment Widget",
			"settings": [
				{
					"type": "number",
					"id": "payment_count",
					"label": "Payment Count",
					"default": 4
				},
				{
					"type": "liquid",
					"id": "text",
					"label": "Widget Text",
					"info": "Or 4 interest-free payments of [ payment ] with ((klarna 40x16)) or ((afterpay 16x16))"
				},
				{
					"type": "header",
					"content": "Icon 1 settings"
				},
				{
					"type": "image_picker",
					"label": "Icon Image",
					"id": "image_1"
				},
				{
					"type": "url",
					"label": "Icon Image URL",
					"id": "link_1"
				},
				{
					"type": "header",
					"content": "Icon 2 settings"
				},
				{
					"type": "image_picker",
					"label": "Icon Image",
					"id": "image_2"
				},
				{
					"type": "url",
					"label": "Icon Image URL",
					"id": "link_2"
				},
				{
					"type": "header",
					"content": "Icon 3 settings"
				},
				{
					"type": "image_picker",
					"label": "Icon Image",
					"id": "image_3"
				},
				{
					"type": "url",
					"label": "Icon Image URL",
					"id": "link_3"
				}
			]
		},
		{
			"type": "promo",
			"name": "Promo",
			"settings": [
				{
					"id": "headline",
					"label": "Headline",
					"type": "text"
				},
				{
					"id": "description",
					"label": "Description",
					"type": "richtext"
				},
				{
					"id": "link_link",
					"label": "Link URL",
					"type": "url"
				},
				{
					"id": "icon",
					"info": "Copy image file url. [View files](/admin/settings/files)",
					"label": "Image (80x80)",
					"type": "text"
				},
				{
					"id": "tag",
					"label": "Tag",
					"info": "Show only if display specific tag. Blank will show on all products using this template.",
					"type": "text"
				}
			]
		},
		{
			"name": "Accordion",
			"type": "accordion",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:accordion_detail_class"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "accordion_detail_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Accordion"
				},
				{
					"type": "checkbox",
					"id": "accordion_detail_attr_open",
					"label": "@global: Open by Default",
					"default": false
				},
				{
					"type": "header",
					"content": "@global: Accordion Summary Settings"
				},
				{
					"type": "text",
					"id": "accordion_title",
					"label": "@global: Accordion Title"
				},
				{
					"type": "select",
					"id": "accordion_summary_class_vertical_padding",
					"label": "@global:  Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "accordion_summary_class_horizontal_padding",
					"label": "@global:  Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "accordion_summary_class_vertical_padding_desktop",
					"label": "@global:  Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "accordion_summary_class_horizontal_padding_desktop",
					"label": "@global:  Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "header",
					"content": "@global: Accordion Panel Settings"
				},
				{
					"type": "richtext",
					"id": "accordion_richtext",
					"label": "@global: Accordion Rich Text"
				},
				{
					"type": "liquid",
					"id": "accordion_liquid",
					"label": "@global: Accordion Liquid"
				},
				{
					"type": "select",
					"id": "accordion_panel_class_vertical_padding",
					"label": "@global:  Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "accordion_panel_class_horizontal_padding",
					"label": "@global:  Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "accordion_panel_class_vertical_padding_desktop",
					"label": "@global:  Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "accordion_panel_class_horizontal_padding_desktop",
					"label": "@global:  Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "header",
					"content": "@global: Additional Settings"
				},
				{
					"type": "checkbox",
					"id": "collapse_padding",
					"label": "@global: Collapse Padding",
					"info": "Collapse vertical padding when accordion is open"
				},
				{
					"type": "checkbox",
					"id": "accordion_detail_attr_data_animate",
					"label": "@global: Enable animation",
					"default": false
				}
			]
		},
		{
			"name": "Bundle Component",
			"type": "bundle-component",
			"settings": [
				{
					"type": "header",
					"content": "Product Form"
				},
				{
					"content": "@include ProductForm",
					"type": "paragraph"
				},
				{
					"type": "product",
					"id": "product",
					"label": "@global: Product"
				},
				{
					"type": "header",
					"content": "Product Header"
				},
				{
					"content": "@include ProductHeader",
					"type": "paragraph"
				},
				{
					"type": "liquid",
					"id": "title_source",
					"label": "@global: Product Title Source",
					"info": "Liquid to extract the product title"
				},
				{
					"type": "liquid",
					"id": "metafield_subtitle_value",
					"label": "@global: Product Metafield Subtitle"
				},
				{
					"type": "checkbox",
					"id": "meta_subtitle_check",
					"label": "@global: Display Subtitle",
					"default": false
				},
				{
					"type": "checkbox",
					"id": "price",
					"label": "@global: Display Price",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "type",
					"label": "@global: Display Product Type",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "reviews",
					"label": "@global: Display Review Summary",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "display_dynamic_product_title",
					"label": "@global: Display Dynamic Product Title",
					"default": false
				},
				{
					"type": "header",
					"content": "Product Heading Dynamic population"
				},
				{
					"type": "text",
					"id": "price_dynamic",
					"label": "Display Price"
				},
				{
					"type": "text",
					"id": "type_dynamic",
					"label": "Display Product Type"
				},
				{
					"type": "text",
					"id": "reviews_dynamic",
					"label": "Display Review Summary"
				},
				{
					"type": "header",
					"content": "Variant Selector"
				},
				{
					"type": "select",
					"id": "variant_selector_class_margin_bottom",
					"label": "Variant Selector Bottom Spacing",
					"options": [
						{
							"value": "@include Spacing prop:mb",
							"label": "Inclusion"
						},
						{
							"value": "mb-0",
							"label": "@global: None"
						},
						{
							"value": "mb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "mb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "mb-xs",
							"label": "@global: XS"
						},
						{
							"value": "mb-sm",
							"label": "@global: SM"
						},
						{
							"value": "mb-md",
							"label": "@global: MD"
						},
						{
							"value": "mb-lg",
							"label": "@global: LG"
						},
						{
							"value": "mb-xl",
							"label": "@global: XL"
						},
						{
							"value": "mb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "mb-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"content": "@include VariantSelector",
					"type": "paragraph"
				},
				{
					"type": "checkbox",
					"id": "history_state",
					"label": "@global: Product State Change",
					"default": true,
					"info": "Change URL and product gallery on product sibling selection"
				},
				{
					"type": "select",
					"id": "availability_model",
					"label": "@global: Option Availability Model",
					"default": "progressive",
					"options": [
						{
							"value": "cascade",
							"label": "Cascade"
						},
						{
							"value": "progressive",
							"label": "Progressive"
						},
						{
							"value": "reflexive",
							"label": "Reflexive"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@global: Cascade: Evaluates availability top-down. Selections in earlier options impact subsequent ones but not vice versa. For instance, choosing in option 1 affects options 2 and 3, but a choice in option 2 only impacts option 3.\n2."
				},
				{
					"type": "paragraph",
					"content": "@global: Progressive: Assesses product options based on all previous choices, successively refining available selections.\n3."
				},
				{
					"type": "paragraph",
					"content": "@global: Reflexive: Availability is dictated only by the latest selection, disregarding prior choices."
				},
				{
					"type": "text",
					"id": "unselected",
					"label": "@global: Unselected Button Text",
					"default": "Select a Size"
				},
				{
					"type": "text",
					"id": "unavailable",
					"label": "@global: Unavailable Button Text",
					"info": "For option combinations that do not have a matching variant",
					"default": "Unavailable"
				},
				{
					"type": "text",
					"id": "out_of_stock",
					"label": "@global: Out of Stock Button Text",
					"default": "Out of Stock"
				},
				{
					"type": "checkbox",
					"id": "enable_strikethrough",
					"label": "@global: Enable Strikethrough on Unavailable products",
					"default": true
				},
				{
					"type": "text",
					"id": "available",
					"label": "@global: Available (Add to Cart) Button Text",
					"default": "Unavailable"
				},
				{
					"type": "range",
					"id": "variants_displayed_on_mobile",
					"label": "@global: Displayed Variants On Mobile",
					"min": 3,
					"max": 6,
					"step": 0.1,
					"default": 3.5
				},
				{
					"type": "checkbox",
					"id": "color_option_wrap_mobile",
					"label": "@global: Wrap Color Option On Mobile"
				},
				{
					"type": "range",
					"id": "variants_displayed_on_desktop",
					"label": "@global: Displayed Variants On Desktop",
					"min": 3,
					"max": 12,
					"step": 1,
					"default": 4
				},
				{
					"type": "header",
					"content": "@global: Product Siblings Settings"
				},
				{
					"type": "liquid",
					"id": "siblings_collection",
					"label": "@global: Product Siblings Collection"
				},
				{
					"type": "product_list",
					"id": "sibling_products",
					"label": "@global: Product Siblings",
					"info": "Specific products override Collection"
				},
				{
					"type": "header",
					"content": "@global: Size Guide Button"
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "@global: Disable Button",
					"default": true
				},
				{
					"type": "text",
					"id": "title",
					"label": "@global: Title",
					"info": "Internal Reference only"
				},
				{
					"type": "liquid",
					"id": "inclusion_js",
					"label": "@global: Inclusion Logic (JavaScript)",
					"info": "JavaScript code that evaluates to a non-false value will output this button"
				},
				{
					"type": "select",
					"id": "style",
					"label": "@global: Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_class_size",
					"label": "@global: Button Size",
					"default": "",
					"options": [
						{
							"value": "button--small",
							"label": "Small"
						},
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "@global: Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "liquid",
					"id": "onclick",
					"label": "@global: Click Event",
					"info": "JavaScript onclick event"
				},
				{
					"type": "header",
					"content": "@global: Variant Segments"
				},
				{
					"type": "select",
					"id": "variant_segment_design",
					"label": "@global: Design",
					"default": "tabs",
					"options": [
						{
							"value": "tabs",
							"label": "Tabs"
						},
						{
							"value": "list",
							"label": "List"
						}
					]
				},
				{
					"type": "text",
					"id": "variant_segment",
					"label": "@global: Variant Segment",
					"info": "This is the namespace and key for the metafield of the variant. (ex. theme.variant_split)",
					"visible_if": "{{ block.settings.variant_segment_design == 'list' }}"
				},
				{
					"type": "text",
					"id": "variant_segment_default",
					"label": "@global: 'Default' Segment Title",
					"default": "Core",
					"visible_if": "{{ block.settings.variant_segment_design == 'list' }}"
				},
				{
					"type": "text",
					"id": "variant_segment_all",
					"label": "@global: 'All Variants' Segment Title",
					"default": "All",
					"info": "Label for the tab that shows all colors",
					"visible_if": "{{ block.settings.variant_segment_design == 'tabs' }}"
				},
				{
					"type": "range",
					"id": "variant_segment_max_tabs",
					"min": 1,
					"max": 5,
					"step": 1,
					"default": 4,
					"label": "@global: Maximum number of tabs to display",
					"visible_if": "{{ block.settings.variant_segment_design == 'tabs' }}"
				},
				{
					"type": "select",
					"id": "variant_segment_default_tab",
					"label": "@global: Default tab",
					"options": [
						{
							"value": "all",
							"label": "All"
						},
						{
							"value": "first",
							"label": "First Available Tab"
						},
						{
							"value": "plp_selection",
							"label": "Based on PLP Selection"
						}
					],
					"default": "all",
					"visible_if": "{{ block.settings.variant_segment_design == 'tabs' }}"
				},
				{
					"type": "select",
					"id": "variant_segment_class_margin",
					"label": "@global: Variant Segment Spacing",
					"default": "mt-md",
					"options": [
						{
							"value": "@include Spacing prop:mt",
							"label": "Inclusion"
						},
						{
							"value": "mt-0",
							"label": "@global: None"
						},
						{
							"value": "mt-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "mt-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "mt-xs",
							"label": "@global: XS"
						},
						{
							"value": "mt-sm",
							"label": "@global: SM"
						},
						{
							"value": "mt-md",
							"label": "@global: MD"
						},
						{
							"value": "mt-lg",
							"label": "@global: LG"
						},
						{
							"value": "mt-xl",
							"label": "@global: XL"
						},
						{
							"value": "mt-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "mt-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "textarea",
					"id": "option_exclude",
					"label": "@global: Option Exclusion",
					"info": "Add the names of any Variant Options you would like to exclude on a new line"
				},
				{
					"type": "header",
					"content": "@global: Alternate Layout"
				},
				{
					"type": "select",
					"id": "variant_selector_class_layout",
					"label": "@global: Layout",
					"options": [
						{
							"label": "Default",
							"value": ""
						},
						{
							"label": "Table",
							"value": "variant-selector--table"
						}
					],
					"default": ""
				},
				{
					"type": "header",
					"content": "@global: Product Color"
				},
				{
					"type": "select",
					"id": "product_color_option_type",
					"label": "@global: Product Color Option Type",
					"default": "image",
					"options": [
						{
							"label": "Image",
							"value": "image"
						},
						{
							"label": "Swatch Color",
							"value": "swatch"
						},
						{
							"label": "Swatch Image",
							"value": "swatch_image"
						}
					]
				},
				{
					"type": "color",
					"id": "product_default_swatch_color_1",
					"label": "@global: Product Default Swatch Color 1",
					"default": "#000000"
				},
				{
					"type": "checkbox",
					"id": "product_color_hover_info",
					"label": "@global: Product Color Hover Info",
					"default": false
				},
				{
					"type": "header",
					"content": "Fit Guide"
				},
				{
					"content": "@include FitGuide",
					"type": "paragraph"
				},
				{
					"type": "paragraph",
					"content": "@global: This feature is powered by tags. On a product the tag format follows: \n\"{Category}:{value}\""
				},
				{
					"type": "paragraph",
					"content": "@global: The product tag can use the option value or a numerical value. For Example: \"size:small\" or \"width:50\" "
				},
				{
					"type": "textarea",
					"label": "@global: Categories",
					"id": "categories"
				},
				{
					"type": "text",
					"label": "@global: Categories (Dynamic)",
					"id": "categories_dynamic"
				},
				{
					"type": "checkbox",
					"label": "@global: Make Collapsable",
					"id": "collapse"
				}
			]
		},
		{
			"type": "gift_card_form",
			"name": "Gift Card Form",
			"settings": [
				{
					"type": "checkbox",
					"label": "Gift card form",
					"id": "gift_card_form",
					"info": "Allows buyers to send gift cards on a scheduled date along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"
				}
			]
		},
		{
			"type": "features",
			"name": "Features",
			"settings": [
				{
					"type": "text",
					"label": "Features List",
					"id": "features_list"
				},
				{
					"type": "textarea",
					"label": "Icon Map",
					"id": "icon_map"
				},
				{
					"type": "textarea",
					"label": "Title Map",
					"id": "title_map"
				},
				{
					"type": "color_background",
					"id": "feature_style_background",
					"label": "Item Background Color",
					"default": "#FFFFFF"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:feature_class, label_prefix:Feature "
				},
				{
					"type": "header",
					"content": "@global: Feature Width Settings"
				},
				{
					"type": "select",
					"id": "feature_class_width",
					"label": "@global: Feature Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_width_desktop",
					"label": "@global: Feature Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:feature_class, label_prefix:Feature, default_direction:flex-col"
				},
				{
					"type": "header",
					"content": "@global: Feature Layout"
				},
				{
					"type": "select",
					"id": "feature_class_display",
					"label": "@global: Feature Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_display_desktop",
					"label": "@global: Feature Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_direction",
					"label": "@global: Feature Direction",
					"default": "flex-col",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_layout",
					"label": "@global: Feature Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_layout_spacing",
					"label": "@global: Feature Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_vertical_padding",
					"label": "@global: Feature Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_horizontal_padding",
					"label": "@global: Feature Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_gap",
					"label": "@global: Feature Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_vertical_padding_desktop",
					"label": "@global: Feature Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_horizontal_padding_desktop",
					"label": "@global: Feature Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "feature_class_gap_desktop",
					"label": "@global: Feature Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_1, label_prefix:Title"
				},
				{
					"type": "header",
					"content": "@global: Title Settings"
				},
				{
					"type": "text",
					"id": "text_1_text",
					"label": "@global: Title Text"
				},
				{
					"type": "liquid",
					"id": "text_1_liquid",
					"label": "@global: Title Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_1_attr_x_text",
					"label": "@global: Title Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_1_element",
					"label": "@global: Title Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_1_class_type_style",
					"label": "@global: Title Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_1_class_type_size",
					"label": "@global: Title Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_1_style_color",
					"label": "@global: Title Color"
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_2, label_prefix:Subtitle"
				},
				{
					"type": "header",
					"content": "@global: Subtitle Settings"
				},
				{
					"type": "text",
					"id": "text_2_text",
					"label": "@global: Subtitle Text"
				},
				{
					"type": "liquid",
					"id": "text_2_liquid",
					"label": "@global: Subtitle Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_2_attr_x_text",
					"label": "@global: Subtitle Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_2_element",
					"label": "@global: Subtitle Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_2_class_type_style",
					"label": "@global: Subtitle Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_2_class_type_size",
					"label": "@global: Subtitle Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_2_style_color",
					"label": "@global: Subtitle Color"
				}
			]
		},
		{
			"type": "toggle-pdp",
			"name": "PDP Toggle",
			"limit": 1,
			"settings": [
				{
					"type": "paragraph",
					"content": "Toggles are managed using a metaobject. [Click here](/admin/content/metaobjects/entries/toggle_nav_pdp) to modify or add toggles."
				},
				{
					"type": "checkbox",
					"label": "Debug pre-rendering",
					"id": "debug_prerendering",
					"default": false
				}
			]
		},
		{
			"type": "@app"
		}
	]
}
{% endschema %}
