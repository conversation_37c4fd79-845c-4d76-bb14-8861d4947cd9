<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Variant Selector DOM Sync Test</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body>
    <h1>Variant Selector DOM Sync Test</h1>
    
    <div id="test-container">
        <h2>Test: Products.select.option() should automatically update DOM</h2>
        
        <!-- Simulate the variant selector structure -->
        <div x-data="{
            productData: $store.products['test-product'],
        }" x-init="() => {
            // Listen for Products:optionSelected events to sync Alpine state
            const handleOptionSelected = (e) => {
                if (e.detail.product === 'test-product') {
                    // The Alpine integration system will automatically update $store.products from window.products
                    // We just need to ensure the DOM input elements are synchronized
                    this.$nextTick(() => {
                        // Trigger any necessary DOM updates to sync radio inputs
                        const input = document.querySelector(`input[name='${e.detail.name}'][value='${e.detail.selected_value}']`);
                        if (input && !input.checked) {
                            input.checked = true;
                        }
                    });
                }
            };
            
            // Add event listener
            window.addEventListener('Products:optionSelected', handleOptionSelected);
            
            // Cleanup on destroy
            this.$cleanup = () => {
                window.removeEventListener('Products:optionSelected', handleOptionSelected);
            };
        }" @destroy="$cleanup && $cleanup()">
            
            <div class="option-group">
                <h3>Size Options</h3>
                <template x-for="value in $store.products['test-product']?.options?.[0]?.values || []">
                    <label>
                        <input 
                            type="radio" 
                            name="Size" 
                            :value="value.value"
                            x-model="$store.products['test-product'].options[0].selected_value"
                            @change="console.log('Radio changed to:', value.value)"
                        />
                        <span x-text="value.value"></span>
                    </label>
                </template>
            </div>
            
            <div class="current-selection">
                <p>Current Selection: <span x-text="$store.products['test-product']?.options?.[0]?.selected_value || 'None'"></span></p>
            </div>
        </div>
        
        <div class="test-controls">
            <h3>Test Controls</h3>
            <button onclick="testSelectOption('Small')">Select Small</button>
            <button onclick="testSelectOption('Medium')">Select Medium</button>
            <button onclick="testSelectOption('Large')">Select Large</button>
        </div>
        
        <div class="test-results">
            <h3>Test Results</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // Mock the Products system and Alpine integration
        window.products = {
            'test-product': {
                handle: 'test-product',
                options: [{
                    name: 'Size',
                    selected_value: 'Medium',
                    values: [
                        { value: 'Small' },
                        { value: 'Medium' },
                        { value: 'Large' }
                    ]
                }]
            }
        };

        // Mock Products.select.option function
        window.Products = {
            select: {
                option: (handle, name, value) => {
                    return new Promise((resolve) => {
                        // Update the internal product state
                        const product = window.products[handle];
                        const option = product.options.find(opt => opt.name === name);
                        if (option) {
                            option.selected_value = value;
                        }
                        
                        // Dispatch the event (this is what the real Products.select.option does)
                        window.dispatchEvent(new CustomEvent('Products:optionSelected', {
                            detail: {
                                product: handle,
                                name: name,
                                selected_value: value
                            }
                        }));
                        
                        resolve();
                    });
                }
            }
        };

        // Mock Alpine integration system
        document.addEventListener('alpine:init', () => {
            Alpine.store('products', window.products);
            
            // Listen for Products:optionSelected events and update Alpine store
            window.addEventListener('Products:optionSelected', () => {
                Alpine.store('products', { ...window.products });
            });
        });

        // Test function
        function testSelectOption(size) {
            const startTime = Date.now();
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML += `<p>Testing selection of ${size}...</p>`;
            
            Products.select.option('test-product', 'Size', size).then(() => {
                setTimeout(() => {
                    const endTime = Date.now();
                    const radioInput = document.querySelector(`input[name="Size"][value="${size}"]`);
                    const isChecked = radioInput ? radioInput.checked : false;
                    const alpineValue = Alpine.store('products')['test-product']?.options?.[0]?.selected_value;
                    
                    resultsDiv.innerHTML += `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ccc;">
                            <strong>Test: Select ${size}</strong><br>
                            Time: ${endTime - startTime}ms<br>
                            Radio Input Checked: ${isChecked ? '✅' : '❌'}<br>
                            Alpine Store Value: ${alpineValue}<br>
                            Success: ${isChecked && alpineValue === size ? '✅' : '❌'}
                        </div>
                    `;
                }, 50); // Small delay to ensure DOM updates
            });
        }

        // Auto-run initial test
        document.addEventListener('alpine:initialized', () => {
            setTimeout(() => {
                testSelectOption('Large');
            }, 100);
        });
    </script>

    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .option-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .option-group label {
            display: block;
            margin: 5px 0;
        }
        
        .test-controls {
            margin: 20px 0;
        }
        
        .test-controls button {
            margin: 5px;
            padding: 10px 15px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .test-controls button:hover {
            background: #005a87;
        }
        
        .current-selection {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 3px;
        }
        
        .test-results {
            margin-top: 30px;
        }
    </style>
</body>
</html>
