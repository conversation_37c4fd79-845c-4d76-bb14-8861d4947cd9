

window['$alpine'] = {

  topics: {

    Cart: {
      source:'cart',
      store:'cart',
      events:['Cart:set', 'Cart:addItems', 'Cart:addItem', 'Cart:change', 'Cart:update']
    },

    Search:{
      source:'search',
      store:'search',
      events:['Search:results','Search:clear','Search:query']
    },

    Product:{
      source:'products',
      store:'products',
      events:['Products:variantSelected', 'Products:optionSelected', 'Products:refresh', 'Products:siblingChange']
    },

    Customer:{
      source:'customer',
      store:'customer',
      events:['Customer:login', 'Customer:update', 'Customer:profileUpdate', 'Customer:profileEdit', 'Customer:subscribe', 'Customer:unsubscribe']
    },

    Wishlist:{
      source:'wishlist',
      store:'wishlist',
      events:['Wishlist:store', 'Wishlist:recall']
    },

    Loyalty:{
      source:'loyalty',
      store:'loyalty',
      events:['Loyalty:update']
    },

    CustomerRequest:{
      // templates against the data sent in the event
      source:'event.detail',
      store:'customerRequest',
      events:['Customer:request']
    },

    QuickAdd:{
      // templates against the data sent in the event
      source:'quickadd',
      store:'quickadd',
      events:['QuickAdd:open','QuickAdd:opened','QuickAdd:select', 'QuickAdd:selected' ]
    },

    Geolocation:{
      source:'geolocation',
      store:'geolocation',
      events:['Geolocation:set', 'Geolocation:mismatch']
    },

    Offers:{
      source:'offers',
      store:'offers',
      events:['Offer:present', 'Offer:presented', 'Offers:declined', 'Offers:accepted', 'Offer:revoked', 'Offers:update' ]
    },

  },

  load: (topic, detail) => {

    if($alpine.topics[topic].source=='event.detail'){
      Alpine.store($alpine.topics[topic].store, detail)
    } else {
      // For products store, maintain object references for Alpine.js reactivity
      if (topic === 'Product') {
        Alpine.store($alpine.topics[topic].store, window[$alpine.topics[topic].source])
      } else {
        Alpine.store($alpine.topics[topic].store, Util.reset(window[$alpine.topics[topic].source]))
      }
    }
    Util.events.dispatch('Templates:render')

  },

  listen: (topic, callback) => {

    $alpine.topics[topic].events.forEach(event => {
        window.addEventListener(event, () => {
            callback()
            return true
        })
    })
    
  },

  init: () => {

    Object.keys($alpine.topics).forEach(topic => {

      $alpine.register(topic, $alpine.topics[topic])

    })

  },

  register: (topic, config) => {

    $alpine.load(topic)

    config.events.forEach(event => {
      window.addEventListener(event, evt => {
        $alpine.load(topic, evt.detail)
      })
    })
  }

}

window.addEventListener('DOMContentLoaded', $alpine.init)

